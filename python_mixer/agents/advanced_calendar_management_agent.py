#!/usr/bin/env python3
"""
Advanced Calendar Management Agent for HVAC CRM
Zaawansowany agent zarządzania kalendarzami z optymalizacją tras dla Warszawy.

Features:
- Trzy główne typy zleceń: ogl<PERSON>d<PERSON>y, montaże, serwisy/naprawy
- Optymalizacja tras dla techników serwisowych
- Planowanie przyszłych zleceń bazowane na mapach
- Inteligentne przełożenie wizyt dla minimalizacji czasu przejazdu
- Integracja z Warsaw OpenStreetMap API
- Vehicle Routing Problem (VRP) optimization
- Dzielnicowa optymalizacja tras
"""

import asyncio
import json
import math
import requests
from typing import Dict, List, Optional, Tuple, Any, NamedTuple
from datetime import datetime, timedelta, time
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger
import numpy as np
from geopy.distance import geodesic
from geopy.geocoders import Nominatim

try:
    import pyomo.environ as pyo
    from pyomo.opt import SolverStatus, TerminationCondition
    PYOMO_AVAILABLE = True
except ImportError:
    logger.warning("Pyomo not available - using simplified optimization")
    PYOMO_AVAILABLE = False

try:
    from ortools.constraint_solver import routing_enums_pb2
    from ortools.constraint_solver import pywrapcp
    ORTOOLS_AVAILABLE = True
except ImportError:
    logger.warning("OR-Tools not available - using basic routing")
    ORTOOLS_AVAILABLE = False


class ServiceType(Enum):
    """Typy zleceń serwisowych HVAC."""
    INSPECTION = "oględziny"      # Oględziny u klientów
    INSTALLATION = "montaż"       # Montaże u klientów  
    SERVICE_REPAIR = "serwis"     # Serwisy i naprawy


class Priority(Enum):
    """Priorytety zleceń."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4


class TechnicianSkill(Enum):
    """Umiejętności techników."""
    BASIC_MAINTENANCE = "basic_maintenance"
    INSTALLATION = "installation"
    ADVANCED_REPAIR = "advanced_repair"
    ELECTRICAL = "electrical"
    REFRIGERATION = "refrigeration"


@dataclass
class Location:
    """Lokalizacja z współrzędnymi geograficznymi."""
    address: str
    latitude: float
    longitude: float
    district: Optional[str] = None
    postal_code: Optional[str] = None
    
    def distance_to(self, other: 'Location') -> float:
        """Oblicz odległość do innej lokalizacji w km."""
        return geodesic((self.latitude, self.longitude), 
                       (other.latitude, other.longitude)).kilometers


@dataclass
class ServiceOrder:
    """Zlecenie serwisowe z pełnymi informacjami."""
    id: str
    service_type: ServiceType
    priority: Priority
    location: Location
    customer_name: str
    customer_phone: str
    estimated_duration: timedelta
    required_skills: List[TechnicianSkill]
    preferred_time_slots: List[Tuple[datetime, datetime]]
    equipment_info: Dict[str, Any] = field(default_factory=dict)
    special_requirements: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    
    def __post_init__(self):
        if self.deadline is None:
            # Ustaw deadline na podstawie priorytetu
            if self.priority == Priority.URGENT:
                self.deadline = self.created_at + timedelta(hours=4)
            elif self.priority == Priority.HIGH:
                self.deadline = self.created_at + timedelta(days=1)
            elif self.priority == Priority.MEDIUM:
                self.deadline = self.created_at + timedelta(days=3)
            else:
                self.deadline = self.created_at + timedelta(weeks=1)


@dataclass
class Technician:
    """Technik serwisowy z umiejętnościami i dostępnością."""
    id: str
    name: str
    skills: List[TechnicianSkill]
    base_location: Location
    working_hours: Tuple[time, time]  # (start, end)
    max_daily_orders: int = 8
    vehicle_capacity: int = 100  # kg
    hourly_rate: float = 80.0  # PLN/h
    travel_speed_kmh: float = 35.0  # średnia prędkość w Warszawie
    
    def can_handle_order(self, order: ServiceOrder) -> bool:
        """Sprawdź czy technik może obsłużyć zlecenie."""
        return any(skill in self.skills for skill in order.required_skills)
    
    def travel_time_to(self, location: Location) -> timedelta:
        """Oblicz czas dojazdu do lokalizacji."""
        distance = self.base_location.distance_to(location)
        hours = distance / self.travel_speed_kmh
        return timedelta(hours=hours)


@dataclass
class ScheduledAppointment:
    """Zaplanowana wizyta z pełnymi szczegółami."""
    order: ServiceOrder
    technician: Technician
    start_time: datetime
    end_time: datetime
    travel_time_before: timedelta
    travel_time_after: timedelta
    route_position: int = 0  # pozycja w trasie dnia
    
    @property
    def total_duration(self) -> timedelta:
        """Całkowity czas włączając dojazdy."""
        return (self.end_time - self.start_time + 
                self.travel_time_before + self.travel_time_after)


class WarsawDistrictOptimizer:
    """Optymalizator tras dla dzielnic Warszawy."""
    
    # Główne dzielnice Warszawy z przybliżonymi współrzędnymi centrum
    WARSAW_DISTRICTS = {
        "Śródmieście": (52.2297, 21.0122),
        "Mokotów": (52.1951, 21.0450),
        "Ochota": (52.2116, 21.0067),
        "Wola": (52.2394, 20.9803),
        "Żoliborz": (52.2656, 20.9814),
        "Praga-Północ": (52.2525, 21.0356),
        "Praga-Południe": (52.2297, 21.0689),
        "Targówek": (52.2897, 21.0489),
        "Rembertów": (52.2597, 21.1489),
        "Wawer": (52.2097, 21.1789),
        "Wilanów": (52.1647, 21.0889),
        "Ursynów": (52.1397, 21.0489),
        "Włochy": (52.1897, 20.9289),
        "Ursus": (52.1997, 20.8889),
        "Bemowo": (52.2597, 20.9089),
        "Białołęka": (52.3097, 21.0089),
        "Bielany": (52.2897, 20.9489),
        "Wesołą": (52.2397, 21.2189)
    }
    
    def __init__(self):
        self.geocoder = Nominatim(user_agent="hvac_calendar_agent")
        
    def get_district_for_location(self, location: Location) -> str:
        """Określ dzielnicę dla lokalizacji."""
        if location.district:
            return location.district
            
        # Znajdź najbliższą dzielnicę
        min_distance = float('inf')
        closest_district = "Śródmieście"
        
        for district, (lat, lon) in self.WARSAW_DISTRICTS.items():
            distance = geodesic((location.latitude, location.longitude), (lat, lon)).kilometers
            if distance < min_distance:
                min_distance = distance
                closest_district = district
                
        return closest_district
    
    def group_orders_by_district(self, orders: List[ServiceOrder]) -> Dict[str, List[ServiceOrder]]:
        """Grupuj zlecenia według dzielnic."""
        district_groups = {}
        
        for order in orders:
            district = self.get_district_for_location(order.location)
            if district not in district_groups:
                district_groups[district] = []
            district_groups[district].append(order)
            
        return district_groups
    
    def calculate_district_priority_score(self, district: str, orders: List[ServiceOrder]) -> float:
        """Oblicz priorytet dzielnicy na podstawie zleceń."""
        if not orders:
            return 0.0
            
        # Wagi dla różnych czynników
        urgency_weight = 0.4
        volume_weight = 0.3
        efficiency_weight = 0.3
        
        # Średni priorytet zleceń
        avg_priority = sum(order.priority.value for order in orders) / len(orders)
        urgency_score = avg_priority / 4.0  # normalizacja do 0-1
        
        # Liczba zleceń (więcej = lepiej dla efektywności)
        volume_score = min(len(orders) / 5.0, 1.0)  # maksymalnie 5 zleceń = 1.0
        
        # Efektywność (kompaktność zleceń w dzielnicy)
        efficiency_score = self._calculate_district_compactness(orders)
        
        total_score = (urgency_weight * urgency_score + 
                      volume_weight * volume_score + 
                      efficiency_weight * efficiency_score)
        
        return total_score
    
    def _calculate_district_compactness(self, orders: List[ServiceOrder]) -> float:
        """Oblicz kompaktność zleceń w dzielnicy (mniejszy rozrzut = lepiej)."""
        if len(orders) <= 1:
            return 1.0
            
        locations = [(order.location.latitude, order.location.longitude) for order in orders]
        
        # Oblicz średnią odległość między wszystkimi parami lokalizacji
        total_distance = 0
        pair_count = 0
        
        for i in range(len(locations)):
            for j in range(i + 1, len(locations)):
                distance = geodesic(locations[i], locations[j]).kilometers
                total_distance += distance
                pair_count += 1
        
        if pair_count == 0:
            return 1.0
            
        avg_distance = total_distance / pair_count
        
        # Konwertuj na score (mniejsza odległość = wyższy score)
        # Zakładamy, że 5km to maksymalna "dobra" odległość w dzielnicy
        compactness_score = max(0, 1 - (avg_distance / 5.0))
        
        return compactness_score


class AdvancedCalendarManagementAgent:
    """
    Zaawansowany agent zarządzania kalendarzami HVAC.

    Capabilities:
    - Optymalizacja tras dla trzech typów zleceń
    - Planowanie bazowane na mapach Warszawy
    - Inteligentne przełożenie wizyt
    - Vehicle Routing Problem optimization
    - Dzielnicowa optymalizacja
    """

    def __init__(self):
        self.district_optimizer = WarsawDistrictOptimizer()
        self.technicians: List[Technician] = []
        self.pending_orders: List[ServiceOrder] = []
        self.scheduled_appointments: List[ScheduledAppointment] = []

        # Konfiguracja optymalizacji
        self.optimization_config = {
            "max_daily_travel_time": timedelta(hours=3),
            "preferred_start_time": time(8, 0),
            "preferred_end_time": time(16, 0),
            "lunch_break_duration": timedelta(minutes=30),
            "buffer_time_between_appointments": timedelta(minutes=15)
        }

        logger.info("Advanced Calendar Management Agent initialized")

    def add_technician(self, technician: Technician):
        """Dodaj technika do systemu."""
        self.technicians.append(technician)
        logger.info(f"Added technician: {technician.name} with skills: {[s.value for s in technician.skills]}")

    def add_service_order(self, order: ServiceOrder):
        """Dodaj nowe zlecenie serwisowe."""
        self.pending_orders.append(order)
        logger.info(f"Added service order: {order.id} ({order.service_type.value}) - Priority: {order.priority.name}")

    async def optimize_daily_schedule(self, target_date: datetime) -> Dict[str, List[ScheduledAppointment]]:
        """
        Optymalizuj harmonogram na dany dzień.

        Returns:
            Dict z kluczami jako ID techników i wartościami jako listy zaplanowanych wizyt
        """
        logger.info(f"Optimizing schedule for {target_date.date()}")

        # Filtruj zlecenia na dany dzień
        daily_orders = self._filter_orders_for_date(target_date)

        if not daily_orders:
            logger.info("No orders for the specified date")
            return {}

        # Grupuj zlecenia według dzielnic
        district_groups = self.district_optimizer.group_orders_by_district(daily_orders)

        # Oblicz priorytety dzielnic
        district_priorities = {}
        for district, orders in district_groups.items():
            priority_score = self.district_optimizer.calculate_district_priority_score(district, orders)
            district_priorities[district] = priority_score

        # Sortuj dzielnice według priorytetów
        sorted_districts = sorted(district_priorities.items(), key=lambda x: x[1], reverse=True)

        logger.info(f"District priorities: {dict(sorted_districts)}")

        # Optymalizuj harmonogram dla każdego technika
        optimized_schedule = {}

        for technician in self.technicians:
            technician_schedule = await self._optimize_technician_schedule(
                technician, daily_orders, sorted_districts, target_date
            )
            optimized_schedule[technician.id] = technician_schedule

        # Usuń zaplanowane zlecenia z listy oczekujących
        scheduled_order_ids = set()
        for appointments in optimized_schedule.values():
            for appointment in appointments:
                scheduled_order_ids.add(appointment.order.id)

        self.pending_orders = [order for order in self.pending_orders
                              if order.id not in scheduled_order_ids]

        logger.info(f"Optimized schedule created for {len(optimized_schedule)} technicians")
        return optimized_schedule

    def _filter_orders_for_date(self, target_date: datetime) -> List[ServiceOrder]:
        """Filtruj zlecenia odpowiednie na dany dzień."""
        filtered_orders = []

        for order in self.pending_orders:
            # Sprawdź czy zlecenie może być wykonane w tym dniu
            if order.deadline and order.deadline.date() < target_date.date():
                continue  # Zlecenie już przeterminowane

            # Sprawdź preferowane sloty czasowe
            has_suitable_slot = False
            for start_time, end_time in order.preferred_time_slots:
                if start_time.date() <= target_date.date() <= end_time.date():
                    has_suitable_slot = True
                    break

            # Jeśli brak preferowanych slotów, sprawdź deadline
            if not has_suitable_slot and order.deadline:
                if target_date.date() <= order.deadline.date():
                    has_suitable_slot = True

            # Jeśli brak deadline i preferowanych slotów, dodaj do rozważenia
            if not has_suitable_slot and not order.deadline and not order.preferred_time_slots:
                has_suitable_slot = True

            if has_suitable_slot:
                filtered_orders.append(order)

        # Sortuj według priorytetu i deadline
        filtered_orders.sort(key=lambda x: (x.priority.value, x.deadline or datetime.max), reverse=True)

        return filtered_orders

    async def _optimize_technician_schedule(
        self,
        technician: Technician,
        available_orders: List[ServiceOrder],
        sorted_districts: List[Tuple[str, float]],
        target_date: datetime
    ) -> List[ScheduledAppointment]:
        """Optymalizuj harmonogram dla konkretnego technika."""

        # Filtruj zlecenia które technik może obsłużyć
        suitable_orders = [order for order in available_orders
                          if technician.can_handle_order(order)]

        if not suitable_orders:
            return []

        # Rozpocznij od bazy technika
        current_location = technician.base_location
        current_time = datetime.combine(target_date.date(), technician.working_hours[0])
        end_time = datetime.combine(target_date.date(), technician.working_hours[1])

        scheduled_appointments = []
        remaining_orders = suitable_orders.copy()

        # Iteruj przez dzielnice według priorytetów
        for district_name, priority_score in sorted_districts:
            if not remaining_orders:
                break

            # Znajdź zlecenia w tej dzielnicy
            district_orders = [order for order in remaining_orders
                             if self.district_optimizer.get_district_for_location(order.location) == district_name]

            if not district_orders:
                continue

            # Optymalizuj trasę w dzielnicy
            district_appointments = await self._optimize_district_route(
                technician, district_orders, current_location, current_time, end_time
            )

            # Dodaj zaplanowane wizyty
            for appointment in district_appointments:
                scheduled_appointments.append(appointment)
                current_location = appointment.order.location
                current_time = appointment.end_time + appointment.travel_time_after

                # Usuń zlecenie z listy pozostałych
                if appointment.order in remaining_orders:
                    remaining_orders.remove(appointment.order)

                # Sprawdź czy nie przekroczono czasu pracy
                if current_time >= end_time:
                    break

            if current_time >= end_time:
                break

        logger.info(f"Scheduled {len(scheduled_appointments)} appointments for {technician.name}")
        return scheduled_appointments

    async def _optimize_district_route(
        self,
        technician: Technician,
        district_orders: List[ServiceOrder],
        start_location: Location,
        start_time: datetime,
        end_time: datetime
    ) -> List[ScheduledAppointment]:
        """Optymalizuj trasę w obrębie dzielnicy."""

        if not district_orders:
            return []

        # Sortuj zlecenia według priorytetu i odległości od punktu startowego
        def order_score(order):
            priority_score = order.priority.value * 10  # Priorytet ma większą wagę
            distance_score = start_location.distance_to(order.location)
            return priority_score - distance_score  # Wyższy priorytet, mniejsza odległość = wyższy score

        sorted_orders = sorted(district_orders, key=order_score, reverse=True)

        # Użyj algorytmu najbliższego sąsiada z optymalizacjami
        if ORTOOLS_AVAILABLE and len(sorted_orders) > 3:
            return await self._optimize_route_with_ortools(
                technician, sorted_orders, start_location, start_time, end_time
            )
        else:
            return await self._optimize_route_greedy(
                technician, sorted_orders, start_location, start_time, end_time
            )

    async def _optimize_route_greedy(
        self,
        technician: Technician,
        orders: List[ServiceOrder],
        start_location: Location,
        start_time: datetime,
        end_time: datetime
    ) -> List[ScheduledAppointment]:
        """Optymalizacja trasy algorytmem zachłannym (greedy)."""

        appointments = []
        current_location = start_location
        current_time = start_time
        remaining_orders = orders.copy()

        while remaining_orders and current_time < end_time:
            # Znajdź najbliższe zlecenie które można wykonać
            best_order = None
            best_score = -1

            for order in remaining_orders:
                # Oblicz czas dojazdu
                travel_time = self._calculate_travel_time(current_location, order.location)
                arrival_time = current_time + travel_time
                completion_time = arrival_time + order.estimated_duration

                # Sprawdź czy zlecenie mieści się w czasie pracy
                if completion_time > end_time:
                    continue

                # Sprawdź preferowane sloty czasowe
                if order.preferred_time_slots:
                    suitable_slot = False
                    for slot_start, slot_end in order.preferred_time_slots:
                        if slot_start <= arrival_time <= slot_end:
                            suitable_slot = True
                            break
                    if not suitable_slot:
                        continue

                # Oblicz score (priorytet vs odległość vs czas)
                priority_score = order.priority.value * 5
                distance_score = 10 / (1 + current_location.distance_to(order.location))
                time_efficiency = 5 / (1 + travel_time.total_seconds() / 3600)

                total_score = priority_score + distance_score + time_efficiency

                if total_score > best_score:
                    best_score = total_score
                    best_order = order

            if not best_order:
                break

            # Zaplanuj wizytę
            travel_time = self._calculate_travel_time(current_location, best_order.location)
            arrival_time = current_time + travel_time
            completion_time = arrival_time + best_order.estimated_duration

            # Dodaj bufor czasowy
            buffer_time = self.optimization_config["buffer_time_between_appointments"]
            next_travel_time = timedelta(minutes=10)  # Szacunkowy czas do następnej lokalizacji

            appointment = ScheduledAppointment(
                order=best_order,
                technician=technician,
                start_time=arrival_time,
                end_time=completion_time,
                travel_time_before=travel_time,
                travel_time_after=next_travel_time,
                route_position=len(appointments)
            )

            appointments.append(appointment)
            remaining_orders.remove(best_order)
            current_location = best_order.location
            current_time = completion_time + buffer_time

        return appointments

    async def _optimize_route_with_ortools(
        self,
        technician: Technician,
        orders: List[ServiceOrder],
        start_location: Location,
        start_time: datetime,
        end_time: datetime
    ) -> List[ScheduledAppointment]:
        """Optymalizacja trasy z użyciem OR-Tools (Vehicle Routing Problem)."""

        if not ORTOOLS_AVAILABLE:
            return await self._optimize_route_greedy(orders, start_location, start_time, end_time)

        try:
            # Przygotuj dane dla OR-Tools
            locations = [start_location] + [order.location for order in orders]

            # Macierz odległości
            distance_matrix = []
            for i, loc1 in enumerate(locations):
                row = []
                for j, loc2 in enumerate(locations):
                    if i == j:
                        row.append(0)
                    else:
                        distance = loc1.distance_to(loc2)
                        # Konwertuj na metry i dodaj czas w sekundach
                        travel_time_seconds = int((distance / technician.travel_speed_kmh) * 3600)
                        row.append(travel_time_seconds)
                row.append(row)

            # Utwórz model VRP
            manager = pywrapcp.RoutingIndexManager(len(locations), 1, 0)
            routing = pywrapcp.RoutingModel(manager)

            # Callback dla odległości
            def distance_callback(from_index, to_index):
                from_node = manager.IndexToNode(from_index)
                to_node = manager.IndexToNode(to_index)
                return distance_matrix[from_node][to_node]

            transit_callback_index = routing.RegisterTransitCallback(distance_callback)
            routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)

            # Ograniczenia czasowe
            max_time = int((end_time - start_time).total_seconds())
            routing.AddDimension(
                transit_callback_index,
                0,  # no slack
                max_time,  # maximum time per vehicle
                True,  # start cumul to zero
                'Time'
            )

            # Parametry wyszukiwania
            search_parameters = pywrapcp.DefaultRoutingSearchParameters()
            search_parameters.first_solution_strategy = (
                routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC
            )
            search_parameters.time_limit.seconds = 10

            # Rozwiąż problem
            solution = routing.SolveWithParameters(search_parameters)

            if solution:
                return self._extract_appointments_from_solution(
                    solution, manager, routing, orders, technician, start_time
                )
            else:
                logger.warning("OR-Tools failed to find solution, falling back to greedy")
                return await self._optimize_route_greedy(
                    technician, orders, start_location, start_time, end_time
                )

        except Exception as e:
            logger.error(f"OR-Tools optimization failed: {e}")
            return await self._optimize_route_greedy(
                technician, orders, start_location, start_time, end_time
            )

    def _calculate_travel_time(self, from_location: Location, to_location: Location) -> timedelta:
        """Oblicz czas podróży między lokalizacjami."""
        distance_km = from_location.distance_to(to_location)

        # Uwzględnij korki w Warszawie - różne prędkości w różnych porach
        current_hour = datetime.now().hour

        if 7 <= current_hour <= 9 or 16 <= current_hour <= 18:
            # Godziny szczytu - wolniejszy ruch
            speed_kmh = 25.0
        elif 10 <= current_hour <= 15:
            # Godziny poza szczytem - szybszy ruch
            speed_kmh = 40.0
        else:
            # Wieczór/noc - średni ruch
            speed_kmh = 35.0

        travel_hours = distance_km / speed_kmh
        return timedelta(hours=travel_hours)

    def _extract_appointments_from_solution(
        self,
        solution,
        manager,
        routing,
        orders: List[ServiceOrder],
        technician: Technician,
        start_time: datetime
    ) -> List[ScheduledAppointment]:
        """Wyciągnij zaplanowane wizyty z rozwiązania OR-Tools."""

        appointments = []
        index = routing.Start(0)
        current_time = start_time
        route_position = 0

        while not routing.IsEnd(index):
            node_index = manager.IndexToNode(index)

            if node_index > 0:  # Pomijamy punkt startowy (index 0)
                order = orders[node_index - 1]

                # Oblicz czasy
                if appointments:
                    prev_location = appointments[-1].order.location
                else:
                    prev_location = technician.base_location

                travel_time = self._calculate_travel_time(prev_location, order.location)
                arrival_time = current_time + travel_time
                completion_time = arrival_time + order.estimated_duration

                appointment = ScheduledAppointment(
                    order=order,
                    technician=technician,
                    start_time=arrival_time,
                    end_time=completion_time,
                    travel_time_before=travel_time,
                    travel_time_after=timedelta(minutes=10),
                    route_position=route_position
                )

                appointments.append(appointment)
                current_time = completion_time + self.optimization_config["buffer_time_between_appointments"]
                route_position += 1

            index = solution.Value(routing.NextVar(index))

        return appointments

    async def plan_future_service_orders(self, days_ahead: int = 30) -> Dict[str, Any]:
        """
        Planuj przyszłe zlecenia serwisowe na podstawie historii i predykcji.

        Args:
            days_ahead: Liczba dni do przodu dla planowania

        Returns:
            Dict z rekomendacjami przyszłych zleceń
        """
        logger.info(f"Planning future service orders for {days_ahead} days ahead")

        future_plan = {
            "recommended_appointments": [],
            "maintenance_schedule": [],
            "capacity_analysis": {},
            "route_optimization_suggestions": []
        }

        # Analiza pojemności techników
        for technician in self.technicians:
            daily_capacity = technician.max_daily_orders
            working_hours = (datetime.combine(datetime.today(), technician.working_hours[1]) -
                           datetime.combine(datetime.today(), technician.working_hours[0])).total_seconds() / 3600

            future_plan["capacity_analysis"][technician.id] = {
                "name": technician.name,
                "daily_capacity": daily_capacity,
                "working_hours": working_hours,
                "skills": [skill.value for skill in technician.skills],
                "base_district": self.district_optimizer.get_district_for_location(technician.base_location)
            }

        # Generuj rekomendacje dla każdego dnia
        for day_offset in range(1, days_ahead + 1):
            target_date = datetime.now() + timedelta(days=day_offset)

            # Przewiduj zapotrzebowanie na podstawie historii
            predicted_demand = self._predict_daily_demand(target_date)

            # Rekomenduj optymalne rozmieszczenie techników
            district_recommendations = self._recommend_district_coverage(target_date, predicted_demand)

            future_plan["recommended_appointments"].append({
                "date": target_date.date().isoformat(),
                "predicted_demand": predicted_demand,
                "district_recommendations": district_recommendations
            })

        # Planowanie konserwacji prewencyjnej
        maintenance_schedule = self._generate_maintenance_schedule(days_ahead)
        future_plan["maintenance_schedule"] = maintenance_schedule

        # Sugestie optymalizacji tras
        route_suggestions = self._generate_route_optimization_suggestions()
        future_plan["route_optimization_suggestions"] = route_suggestions

        logger.info(f"Future planning completed with {len(future_plan['recommended_appointments'])} daily recommendations")
        return future_plan

    def _predict_daily_demand(self, target_date: datetime) -> Dict[str, int]:
        """Przewiduj zapotrzebowanie na dany dzień."""

        # Bazowe zapotrzebowanie według typu dnia
        weekday = target_date.weekday()

        if weekday < 5:  # Poniedziałek-Piątek
            base_demand = {
                ServiceType.INSPECTION.value: 3,
                ServiceType.INSTALLATION.value: 2,
                ServiceType.SERVICE_REPAIR.value: 4
            }
        else:  # Weekend
            base_demand = {
                ServiceType.INSPECTION.value: 1,
                ServiceType.INSTALLATION.value: 1,
                ServiceType.SERVICE_REPAIR.value: 2
            }

        # Modyfikatory sezonowe
        month = target_date.month

        if month in [6, 7, 8]:  # Lato - więcej instalacji klimatyzacji
            base_demand[ServiceType.INSTALLATION.value] *= 1.5
            base_demand[ServiceType.SERVICE_REPAIR.value] *= 1.3
        elif month in [12, 1, 2]:  # Zima - więcej napraw ogrzewania
            base_demand[ServiceType.SERVICE_REPAIR.value] *= 1.4
        elif month in [3, 4, 5]:  # Wiosna - więcej przeglądów
            base_demand[ServiceType.INSPECTION.value] *= 1.3

        # Konwertuj na int
        return {k: int(v) for k, v in base_demand.items()}

    def _recommend_district_coverage(self, target_date: datetime, predicted_demand: Dict[str, int]) -> List[Dict[str, Any]]:
        """Rekomenduj pokrycie dzielnic na dany dzień."""

        recommendations = []
        total_demand = sum(predicted_demand.values())

        # Sortuj dzielnice według historycznego zapotrzebowania
        district_priorities = []
        for district_name, coords in self.district_optimizer.WARSAW_DISTRICTS.items():
            # Symuluj historyczne zapotrzebowanie (w rzeczywistej implementacji użyj prawdziwych danych)
            historical_demand = self._simulate_historical_demand(district_name, target_date)
            district_priorities.append((district_name, historical_demand, coords))

        # Sortuj według zapotrzebowania
        district_priorities.sort(key=lambda x: x[1], reverse=True)

        # Rekomenduj alokację techników
        available_technicians = len(self.technicians)

        for i, (district_name, demand, coords) in enumerate(district_priorities[:available_technicians]):
            recommended_technicians = max(1, int((demand / total_demand) * available_technicians))

            recommendations.append({
                "district": district_name,
                "predicted_demand": demand,
                "recommended_technicians": recommended_technicians,
                "priority_score": demand,
                "coordinates": coords
            })

        return recommendations

    def _simulate_historical_demand(self, district_name: str, target_date: datetime) -> float:
        """Symuluj historyczne zapotrzebowanie dla dzielnicy."""

        # Bazowe zapotrzebowanie według typu dzielnicy
        district_demand_map = {
            "Śródmieście": 8.5,  # Wysokie zapotrzebowanie - biura
            "Mokotów": 7.2,     # Wysokie - mieszkania + biura
            "Wilanów": 6.8,     # Wysokie - nowe osiedla
            "Ursynów": 6.5,     # Wysokie - duże osiedla
            "Ochota": 5.8,      # Średnie
            "Wola": 5.5,        # Średnie
            "Żoliborz": 5.2,    # Średnie
            "Praga-Południe": 4.8,  # Średnie
            "Praga-Północ": 4.5,    # Średnie-niskie
            "Targówek": 4.2,    # Średnie-niskie
            "Bemowo": 4.0,      # Średnie-niskie
            "Bielany": 3.8,     # Niskie
            "Białołęka": 3.5,   # Niskie
            "Włochy": 3.2,      # Niskie
            "Ursus": 3.0,       # Niskie
            "Rembertów": 2.8,   # Niskie
            "Wawer": 2.5,       # Niskie
            "Wesołą": 2.2       # Niskie
        }

        base_demand = district_demand_map.get(district_name, 3.0)

        # Modyfikatory czasowe
        weekday = target_date.weekday()
        if weekday >= 5:  # Weekend
            base_demand *= 0.6

        # Modyfikator sezonowy
        month = target_date.month
        if month in [6, 7, 8]:  # Lato
            base_demand *= 1.3
        elif month in [12, 1, 2]:  # Zima
            base_demand *= 1.1

        return base_demand

    def _generate_maintenance_schedule(self, days_ahead: int) -> List[Dict[str, Any]]:
        """Generuj harmonogram konserwacji prewencyjnej."""

        maintenance_schedule = []

        # Przykładowy harmonogram konserwacji
        maintenance_types = [
            {
                "type": "Przegląd klimatyzacji",
                "frequency_days": 90,
                "duration_hours": 2,
                "required_skills": [TechnicianSkill.BASIC_MAINTENANCE],
                "season_preference": [3, 4, 5, 9, 10]  # Wiosna i jesień
            },
            {
                "type": "Serwis ogrzewania",
                "frequency_days": 180,
                "duration_hours": 3,
                "required_skills": [TechnicianSkill.ADVANCED_REPAIR],
                "season_preference": [9, 10, 11]  # Jesień
            },
            {
                "type": "Kontrola wentylacji",
                "frequency_days": 60,
                "duration_hours": 1.5,
                "required_skills": [TechnicianSkill.BASIC_MAINTENANCE],
                "season_preference": list(range(1, 13))  # Cały rok
            }
        ]

        for day_offset in range(1, days_ahead + 1):
            target_date = datetime.now() + timedelta(days=day_offset)

            for maintenance_type in maintenance_types:
                # Sprawdź czy to odpowiednia pora roku
                if target_date.month in maintenance_type["season_preference"]:
                    # Symuluj potrzebę konserwacji (w rzeczywistości bazuj na danych klientów)
                    if day_offset % maintenance_type["frequency_days"] == 0:
                        maintenance_schedule.append({
                            "date": target_date.date().isoformat(),
                            "type": maintenance_type["type"],
                            "estimated_duration": maintenance_type["duration_hours"],
                            "required_skills": [skill.value for skill in maintenance_type["required_skills"]],
                            "priority": "medium",
                            "recommended_districts": ["Śródmieście", "Mokotów", "Wilanów"]  # Przykład
                        })

        return maintenance_schedule

    def _generate_route_optimization_suggestions(self) -> List[Dict[str, Any]]:
        """Generuj sugestie optymalizacji tras."""

        suggestions = []

        # Analiza obecnych tras i sugestie
        suggestions.append({
            "type": "district_clustering",
            "title": "Grupowanie zleceń według dzielnic",
            "description": "Grupuj zlecenia w tej samej dzielnicy w tym samym dniu dla minimalizacji czasu przejazdu",
            "potential_savings": "20-30% czasu przejazdu",
            "implementation": "Automatyczne grupowanie w algorytmie planowania"
        })

        suggestions.append({
            "type": "time_slot_optimization",
            "title": "Optymalizacja slotów czasowych",
            "description": "Oferuj klientom preferowane sloty czasowe bazując na optymalnych trasach",
            "potential_savings": "15-25% czasu przejazdu",
            "implementation": "Dynamiczne sloty czasowe w systemie rezerwacji"
        })

        suggestions.append({
            "type": "technician_specialization",
            "title": "Specjalizacja techników według dzielnic",
            "description": "Przypisz techników do konkretnych dzielnic dla lepszej znajomości terenu",
            "potential_savings": "10-15% czasu przejazdu + lepsza obsługa klienta",
            "implementation": "Stałe przypisanie techników do dzielnic"
        })

        suggestions.append({
            "type": "predictive_scheduling",
            "title": "Predykcyjne planowanie",
            "description": "Przewiduj zapotrzebowanie i przygotowuj techników w odpowiednich lokalizacjach",
            "potential_savings": "25-35% czasu reakcji",
            "implementation": "AI-powered demand prediction"
        })

        return suggestions

    def get_optimization_statistics(self) -> Dict[str, Any]:
        """Pobierz statystyki optymalizacji."""

        total_appointments = len(self.scheduled_appointments)

        if total_appointments == 0:
            return {"message": "No appointments scheduled yet"}

        # Oblicz statystyki
        total_travel_time = sum(
            (apt.travel_time_before + apt.travel_time_after).total_seconds() / 3600
            for apt in self.scheduled_appointments
        )

        total_service_time = sum(
            (apt.end_time - apt.start_time).total_seconds() / 3600
            for apt in self.scheduled_appointments
        )

        # Grupuj według techników
        technician_stats = {}
        for apt in self.scheduled_appointments:
            tech_id = apt.technician.id
            if tech_id not in technician_stats:
                technician_stats[tech_id] = {
                    "name": apt.technician.name,
                    "appointments": 0,
                    "travel_time": 0,
                    "service_time": 0,
                    "districts_covered": set()
                }

            technician_stats[tech_id]["appointments"] += 1
            technician_stats[tech_id]["travel_time"] += (apt.travel_time_before + apt.travel_time_after).total_seconds() / 3600
            technician_stats[tech_id]["service_time"] += (apt.end_time - apt.start_time).total_seconds() / 3600
            technician_stats[tech_id]["districts_covered"].add(
                self.district_optimizer.get_district_for_location(apt.order.location)
            )

        # Konwertuj sets na listy dla JSON serialization
        for tech_id in technician_stats:
            technician_stats[tech_id]["districts_covered"] = list(technician_stats[tech_id]["districts_covered"])

        efficiency_ratio = total_service_time / (total_service_time + total_travel_time) if (total_service_time + total_travel_time) > 0 else 0

        return {
            "total_appointments": total_appointments,
            "total_travel_time_hours": round(total_travel_time, 2),
            "total_service_time_hours": round(total_service_time, 2),
            "efficiency_ratio": round(efficiency_ratio, 3),
            "average_travel_per_appointment": round(total_travel_time / total_appointments, 2) if total_appointments > 0 else 0,
            "technician_statistics": technician_stats,
            "optimization_score": round(efficiency_ratio * 100, 1)
        }
