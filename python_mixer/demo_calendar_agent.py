#!/usr/bin/env python3
"""
Demo script for Advanced Calendar Management Agent
Demonstracja zaawansowanego agenta zarządzania kalendarzami HVAC.

Usage:
    python demo_calendar_agent.py
"""

import asyncio
import json
from datetime import datetime, timedelta, time
from agents.advanced_calendar_management_agent import (
    AdvancedCalendarManagementAgent, ServiceType, Priority, TechnicianSkill,
    Location, ServiceOrder, Technician
)
from loguru import logger

def create_demo_technicians():
    """Stwórz demo techników dla Warszawy."""
    technicians = []
    
    # Technik 1 - Mokot<PERSON> (Specjalista instalacji)
    tech1 = Technician(
        id="TECH_001",
        name="<PERSON><PERSON>",
        skills=[TechnicianSkill.INSTALLATION, TechnicianSkill.ELECTRICAL, TechnicianSkill.BASIC_MAINTENANCE],
        base_location=Location(
            address="ul. Puławska 100, Warszawa",
            latitude=52.1951,
            longitude=21.0450,
            district="Mokotów",
            postal_code="02-595"
        ),
        working_hours=(time(8, 0), time(16, 0)),
        max_daily_orders=6,
        hourly_rate=85.0
    )
    technicians.append(tech1)
    
    # Technik 2 - Śródmieście (Specjalista napraw)
    tech2 = Technician(
        id="TECH_002", 
        name="Piotr Nowak",
        skills=[TechnicianSkill.ADVANCED_REPAIR, TechnicianSkill.REFRIGERATION, TechnicianSkill.ELECTRICAL],
        base_location=Location(
            address="ul. Marszałkowska 50, Warszawa",
            latitude=52.2297,
            longitude=21.0122,
            district="Śródmieście",
            postal_code="00-676"
        ),
        working_hours=(time(7, 30), time(15, 30)),
        max_daily_orders=8,
        hourly_rate=95.0,
        travel_speed_kmh=30.0  # Wolniej w centrum
    )
    technicians.append(tech2)
    
    # Technik 3 - Wilanów (Uniwersalny)
    tech3 = Technician(
        id="TECH_003",
        name="Anna Wiśniewska", 
        skills=[TechnicianSkill.BASIC_MAINTENANCE, TechnicianSkill.INSTALLATION, TechnicianSkill.ADVANCED_REPAIR],
        base_location=Location(
            address="ul. Klimczaka 1, Warszawa",
            latitude=52.1647,
            longitude=21.0889,
            district="Wilanów",
            postal_code="02-797"
        ),
        working_hours=(time(8, 30), time(16, 30)),
        max_daily_orders=7,
        hourly_rate=90.0,
        travel_speed_kmh=40.0  # Szybciej na południu
    )
    technicians.append(tech3)
    
    return technicians

def create_demo_orders():
    """Stwórz demo zlecenia serwisowe."""
    orders = []
    
    # Zlecenie 1 - Oględziny w Mokotowie
    order1 = ServiceOrder(
        id="ORDER_001",
        service_type=ServiceType.INSPECTION,
        priority=Priority.MEDIUM,
        location=Location(
            address="ul. Dolna 5, Warszawa",
            latitude=52.1900,
            longitude=21.0400,
            district="Mokotów"
        ),
        customer_name="Jan Kowalski",
        customer_phone="+48 123 456 789",
        estimated_duration=timedelta(hours=1.5),
        required_skills=[TechnicianSkill.BASIC_MAINTENANCE],
        equipment_info={"type": "klimatyzacja", "model": "LG S12ET"}
    )
    orders.append(order1)
    
    # Zlecenie 2 - Montaż w Wilanowie
    order2 = ServiceOrder(
        id="ORDER_002",
        service_type=ServiceType.INSTALLATION,
        priority=Priority.HIGH,
        location=Location(
            address="ul. Sarmacka 10, Warszawa",
            latitude=52.1600,
            longitude=21.0800,
            district="Wilanów"
        ),
        customer_name="Maria Nowak",
        customer_phone="+48 987 654 321",
        estimated_duration=timedelta(hours=4),
        required_skills=[TechnicianSkill.INSTALLATION, TechnicianSkill.ELECTRICAL],
        equipment_info={"type": "klimatyzacja", "model": "Daikin FTXS25K"}
    )
    orders.append(order2)
    
    # Zlecenie 3 - Naprawa w Śródmieściu (URGENT)
    order3 = ServiceOrder(
        id="ORDER_003",
        service_type=ServiceType.SERVICE_REPAIR,
        priority=Priority.URGENT,
        location=Location(
            address="ul. Nowy Świat 20, Warszawa",
            latitude=52.2350,
            longitude=21.0150,
            district="Śródmieście"
        ),
        customer_name="Piotr Wiśniewski",
        customer_phone="+48 555 123 456",
        estimated_duration=timedelta(hours=2.5),
        required_skills=[TechnicianSkill.ADVANCED_REPAIR, TechnicianSkill.REFRIGERATION],
        equipment_info={"type": "chiller", "model": "Carrier 30XA"}
    )
    orders.append(order3)
    
    # Zlecenie 4 - Serwis w Mokotowie
    order4 = ServiceOrder(
        id="ORDER_004",
        service_type=ServiceType.SERVICE_REPAIR,
        priority=Priority.MEDIUM,
        location=Location(
            address="ul. Woronicza 15, Warszawa",
            latitude=52.1850,
            longitude=21.0350,
            district="Mokotów"
        ),
        customer_name="Anna Kowalczyk",
        customer_phone="+48 666 789 012",
        estimated_duration=timedelta(hours=2),
        required_skills=[TechnicianSkill.BASIC_MAINTENANCE],
        equipment_info={"type": "wentylacja", "model": "Systemair"}
    )
    orders.append(order4)
    
    # Zlecenie 5 - Oględziny w Wilanowie
    order5 = ServiceOrder(
        id="ORDER_005",
        service_type=ServiceType.INSPECTION,
        priority=Priority.LOW,
        location=Location(
            address="ul. Branickiego 25, Warszawa",
            latitude=52.1700,
            longitude=21.0900,
            district="Wilanów"
        ),
        customer_name="Tomasz Zieliński",
        customer_phone="+48 777 345 678",
        estimated_duration=timedelta(hours=1),
        required_skills=[TechnicianSkill.BASIC_MAINTENANCE],
        equipment_info={"type": "klimatyzacja", "model": "Mitsubishi MSZ-LN25VG"}
    )
    orders.append(order5)
    
    return orders

async def demo_calendar_optimization():
    """Demonstracja optymalizacji kalendarza."""
    logger.info("🚀 Starting Advanced Calendar Management Demo")
    
    # Inicjalizuj agenta
    agent = AdvancedCalendarManagementAgent()
    
    # Dodaj techników
    technicians = create_demo_technicians()
    for tech in technicians:
        agent.add_technician(tech)
    
    # Dodaj zlecenia
    orders = create_demo_orders()
    for order in orders:
        agent.add_service_order(order)
    
    logger.info(f"✅ Configured {len(technicians)} technicians and {len(orders)} orders")
    
    # Optymalizuj harmonogram na jutro
    target_date = datetime.now() + timedelta(days=1)
    logger.info(f"🗺️ Optimizing schedule for {target_date.date()}")
    
    optimized_schedule = await agent.optimize_daily_schedule(target_date)
    
    # Wyświetl wyniki
    logger.info("📋 OPTIMIZED SCHEDULE:")
    logger.info("=" * 60)
    
    for tech_id, appointments in optimized_schedule.items():
        tech_name = next((t.name for t in technicians if t.id == tech_id), tech_id)
        logger.info(f"\n👤 {tech_name} ({tech_id}):")
        
        if not appointments:
            logger.info("   No appointments scheduled")
            continue
            
        for i, apt in enumerate(appointments, 1):
            logger.info(f"   {i}. {apt.start_time.strftime('%H:%M')}-{apt.end_time.strftime('%H:%M')} | "
                       f"{apt.order.service_type.value.upper()} | {apt.order.customer_name}")
            logger.info(f"      📍 {apt.order.location.address}")
            logger.info(f"      🚗 Travel: {apt.travel_time_before} | Priority: {apt.order.priority.name}")
    
    # Statystyki optymalizacji
    stats = agent.get_optimization_statistics()
    logger.info("\n📊 OPTIMIZATION STATISTICS:")
    logger.info("=" * 60)
    logger.info(f"Total appointments: {stats['total_appointments']}")
    logger.info(f"Total travel time: {stats['total_travel_time_hours']}h")
    logger.info(f"Total service time: {stats['total_service_time_hours']}h")
    logger.info(f"Efficiency ratio: {stats['efficiency_ratio']:.1%}")
    logger.info(f"Optimization score: {stats['optimization_score']}%")
    
    # Planowanie przyszłych zleceń
    logger.info("\n🔮 FUTURE PLANNING:")
    logger.info("=" * 60)
    
    future_plan = await agent.plan_future_service_orders(days_ahead=7)
    
    logger.info(f"Capacity analysis for {len(future_plan['capacity_analysis'])} technicians:")
    for tech_id, capacity in future_plan['capacity_analysis'].items():
        logger.info(f"  👤 {capacity['name']}: {capacity['daily_capacity']} orders/day, "
                   f"{capacity['working_hours']}h, base: {capacity['base_district']}")
    
    logger.info(f"\nRoute optimization suggestions ({len(future_plan['route_optimization_suggestions'])}):")
    for suggestion in future_plan['route_optimization_suggestions']:
        logger.info(f"  🚀 {suggestion['title']}: {suggestion['potential_savings']}")
    
    logger.info("\n✅ Demo completed successfully!")
    
    return {
        "optimized_schedule": optimized_schedule,
        "statistics": stats,
        "future_plan": future_plan
    }

def main():
    """Główna funkcja demo."""
    try:
        # Uruchom demo
        result = asyncio.run(demo_calendar_optimization())
        
        # Zapisz wyniki do pliku
        output_file = f"calendar_demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Konwertuj datetime objects na strings dla JSON
        def datetime_converter(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, timedelta):
                return str(obj)
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            return str(obj)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=datetime_converter)
        
        logger.info(f"📄 Results saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise

if __name__ == "__main__":
    main()
