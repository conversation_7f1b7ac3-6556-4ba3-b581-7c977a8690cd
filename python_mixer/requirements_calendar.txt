# Advanced Calendar Management Agent Requirements
# Zaawansowany agent z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kalendarzami HVAC

# Core dependencies
geopy>=2.3.0              # Geocoding and distance calculations
numpy>=1.24.0             # Numerical computations
loguru>=0.7.0             # Enhanced logging

# Route optimization libraries
ortools>=9.7.2963         # Google OR-Tools for Vehicle Routing Problem (VRP)
pyomo>=6.6.0              # Mathematical optimization modeling

# Optional optimization solvers
# Install one of these for enhanced optimization:
# - CPLEX (commercial, requires license)
# - Gurobi (commercial, requires license)  
# - GLPK (free, install with: conda install -c conda-forge glpk)
# - CBC (free, install with: conda install -c conda-forge coincbc)

# Async support
asyncio                   # Built-in async support

# Data handling
dataclasses               # Built-in dataclass support (Python 3.7+)
typing                    # Built-in type hints
enum                      # Built-in enum support

# Installation commands:
# pip install geopy ortools pyomo numpy loguru

# For enhanced optimization (optional):
# conda install -c conda-forge glpk coincbc

# System requirements:
# - Python 3.8+
# - 4GB+ RAM for large optimization problems
# - Multi-core CPU recommended for OR-Tools

# Warsaw-specific features:
# - OpenStreetMap integration via geopy
# - District-based route optimization
# - Real-time traffic consideration
# - Polish address geocoding support
