#!/usr/bin/env python3
"""
HVAC CRM Database Validation Agent (Simplified)
Comprehensive database consistency validation for HVAC CRM system
"""

import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

import psycopg2
import psycopg2.extras

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_CONFIG = {
    'host': '**************',
    'port': '5432',
    'database': 'hvacdb',
    'user': 'hvacdb',
    'password': 'blaeritipol'
}

class SeverityLevel(Enum):
    """Issue severity levels"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"

@dataclass
class ValidationIssue:
    """Represents a data validation issue"""
    table: str
    record_id: Optional[str]
    field: Optional[str]
    issue_type: str
    severity: SeverityLevel
    description: str
    suggested_fix: str
    sql_query: Optional[str] = None

@dataclass
class ValidationReport:
    """Comprehensive validation report"""
    timestamp: datetime
    total_records_checked: int
    issues_found: List[ValidationIssue]
    summary: Dict[str, Any]
    recommendations: List[str]

class DatabaseValidator:
    """Core database validation engine"""
    
    def __init__(self):
        self.conn = None
        self._connect()
        
    def _connect(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(**DATABASE_CONFIG)
            self.conn.autocommit = True
            logger.info("✅ Connected to HVAC CRM database")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
            
    def _execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute query and return results as list of dictionaries"""
        try:
            with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, params)
                return [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            return []
            
    def _get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get comprehensive table information"""
        query = """
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length
        FROM information_schema.columns 
        WHERE table_name = %s 
        ORDER BY ordinal_position
        """
        columns = self._execute_query(query, (table_name,))
        
        # Get row count
        count_query = f"SELECT COUNT(*) as count FROM {table_name}"
        count_result = self._execute_query(count_query)
        row_count = count_result[0]['count'] if count_result else 0
        
        return {
            'table_name': table_name,
            'columns': columns,
            'row_count': row_count
        }
        
    def validate_customer_records(self) -> List[ValidationIssue]:
        """Validate individual customer records for completeness and accuracy"""
        issues = []
        
        # Check for missing required fields
        required_fields = ['name', 'email', 'phone']
        for field in required_fields:
            query = f"""
            SELECT id, {field}
            FROM customers 
            WHERE {field} IS NULL OR TRIM({field}) = ''
            LIMIT 100
            """
            missing_records = self._execute_query(query)
            
            for record in missing_records:
                issues.append(ValidationIssue(
                    table='customers',
                    record_id=str(record['id']),
                    field=field,
                    issue_type='missing_required_field',
                    severity=SeverityLevel.CRITICAL,
                    description=f"Customer {record['id']} missing required field: {field}",
                    suggested_fix=f"Update customer record with valid {field}",
                    sql_query=f"UPDATE customers SET {field} = 'REQUIRED_VALUE' WHERE id = {record['id']}"
                ))
        
        # Validate email formats
        email_validation_query = r"""
        SELECT id, email
        FROM customers 
        WHERE email IS NOT NULL 
        AND email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
        LIMIT 100
        """
        invalid_emails = self._execute_query(email_validation_query)
        
        for record in invalid_emails:
            issues.append(ValidationIssue(
                table='customers',
                record_id=str(record['id']),
                field='email',
                issue_type='invalid_email_format',
                severity=SeverityLevel.WARNING,
                description=f"Customer {record['id']} has invalid email format: {record['email']}",
                suggested_fix="Update with valid email format",
                sql_query=f"UPDATE customers SET email = '<EMAIL>' WHERE id = {record['id']}"
            ))
        
        # Validate phone number formats
        phone_validation_query = r"""
        SELECT id, phone
        FROM customers 
        WHERE phone IS NOT NULL 
        AND phone !~ '^[+]?[0-9\s\-\(\)]{7,15}$'
        LIMIT 100
        """
        invalid_phones = self._execute_query(phone_validation_query)
        
        for record in invalid_phones:
            issues.append(ValidationIssue(
                table='customers',
                record_id=str(record['id']),
                field='phone',
                issue_type='invalid_phone_format',
                severity=SeverityLevel.WARNING,
                description=f"Customer {record['id']} has invalid phone format: {record['phone']}",
                suggested_fix="Update with valid phone format",
                sql_query=f"UPDATE customers SET phone = 'CORRECTED_PHONE' WHERE id = {record['id']}"
            ))
        
        return issues
        
    def find_duplicate_customers(self) -> List[ValidationIssue]:
        """Identify potential duplicate customer entries"""
        issues = []
        
        # Find duplicates by email
        email_duplicates_query = """
        SELECT email, array_agg(id) as customer_ids, COUNT(*) as count
        FROM customers 
        WHERE email IS NOT NULL AND TRIM(email) != ''
        GROUP BY LOWER(email)
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT 50
        """
        email_duplicates = self._execute_query(email_duplicates_query)
        
        for duplicate in email_duplicates:
            customer_ids = duplicate['customer_ids']
            issues.append(ValidationIssue(
                table='customers',
                record_id=','.join(map(str, customer_ids)),
                field='email',
                issue_type='duplicate_email',
                severity=SeverityLevel.WARNING,
                description=f"Duplicate email '{duplicate['email']}' found in {duplicate['count']} records: {customer_ids}",
                suggested_fix="Merge duplicate records or update email addresses",
                sql_query=f"-- Review customers with IDs: {customer_ids} for potential merge"
            ))
        
        # Find duplicates by phone
        phone_duplicates_query = """
        SELECT phone, array_agg(id) as customer_ids, COUNT(*) as count
        FROM customers 
        WHERE phone IS NOT NULL AND TRIM(phone) != ''
        GROUP BY REGEXP_REPLACE(phone, '[^0-9]', '', 'g')
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT 50
        """
        phone_duplicates = self._execute_query(phone_duplicates_query)
        
        for duplicate in phone_duplicates:
            customer_ids = duplicate['customer_ids']
            issues.append(ValidationIssue(
                table='customers',
                record_id=','.join(map(str, customer_ids)),
                field='phone',
                issue_type='duplicate_phone',
                severity=SeverityLevel.WARNING,
                description=f"Duplicate phone '{duplicate['phone']}' found in {duplicate['count']} records: {customer_ids}",
                suggested_fix="Merge duplicate records or update phone numbers",
                sql_query=f"-- Review customers with IDs: {customer_ids} for potential merge"
            ))
        
        return issues
        
    def validate_foreign_keys(self) -> List[ValidationIssue]:
        """Ensure all foreign key relationships are valid"""
        issues = []
        
        # Check addresses -> customers foreign key
        orphaned_addresses_query = """
        SELECT a.id, a.customer_id
        FROM addresses a
        LEFT JOIN customers c ON a.customer_id = c.id
        WHERE c.id IS NULL
        LIMIT 100
        """
        orphaned_addresses = self._execute_query(orphaned_addresses_query)
        
        for address in orphaned_addresses:
            issues.append(ValidationIssue(
                table='addresses',
                record_id=str(address['id']),
                field='customer_id',
                issue_type='orphaned_foreign_key',
                severity=SeverityLevel.CRITICAL,
                description=f"Address {address['id']} references non-existent customer {address['customer_id']}",
                suggested_fix="Delete orphaned address or create missing customer",
                sql_query=f"DELETE FROM addresses WHERE id = {address['id']} -- OR create missing customer"
            ))
        
        return issues
        
    def check_database_integrity(self) -> List[ValidationIssue]:
        """Perform comprehensive database consistency checks"""
        issues = []
        
        # Check for NULL values in critical fields
        critical_null_checks = [
            ('customers', 'id', 'primary_key_null'),
            ('addresses', 'id', 'primary_key_null'),
        ]
        
        for table, field, issue_type in critical_null_checks:
            query = f"SELECT COUNT(*) as count FROM {table} WHERE {field} IS NULL"
            result = self._execute_query(query)
            
            if result and result[0]['count'] > 0:
                issues.append(ValidationIssue(
                    table=table,
                    record_id=None,
                    field=field,
                    issue_type=issue_type,
                    severity=SeverityLevel.CRITICAL,
                    description=f"Found {result[0]['count']} NULL values in {table}.{field}",
                    suggested_fix=f"Investigate and fix NULL values in {table}.{field}",
                    sql_query=f"SELECT * FROM {table} WHERE {field} IS NULL"
                ))
        
        return issues
        
    def generate_data_quality_report(self) -> ValidationReport:
        """Generate comprehensive data quality report"""
        logger.info("🔍 Starting comprehensive database validation...")
        
        all_issues = []
        
        # Run all validation checks
        logger.info("Validating customer records...")
        all_issues.extend(self.validate_customer_records())
        
        logger.info("Finding duplicate customers...")
        all_issues.extend(self.find_duplicate_customers())
        
        logger.info("Validating foreign keys...")
        all_issues.extend(self.validate_foreign_keys())
        
        logger.info("Checking database integrity...")
        all_issues.extend(self.check_database_integrity())
        
        # Generate summary statistics
        total_records = 0
        table_info = {}
        
        tables = ['customers', 'addresses', 'enhanced_email_analysis']
        for table in tables:
            try:
                info = self._get_table_info(table)
                table_info[table] = info
                total_records += info['row_count']
            except Exception as e:
                logger.warning(f"Could not get info for table {table}: {e}")
        
        # Categorize issues by severity
        critical_issues = [i for i in all_issues if i.severity == SeverityLevel.CRITICAL]
        warning_issues = [i for i in all_issues if i.severity == SeverityLevel.WARNING]
        info_issues = [i for i in all_issues if i.severity == SeverityLevel.INFO]
        
        # Generate recommendations
        recommendations = []
        
        if critical_issues:
            recommendations.append("🚨 CRITICAL: Address critical issues immediately to prevent data corruption")
            recommendations.append("Focus on orphaned foreign keys and missing required fields")
        
        if warning_issues:
            recommendations.append("⚠️ WARNING: Review and fix data format issues to improve data quality")
            recommendations.append("Implement data validation at input level to prevent future issues")
        
        recommendations.append("🔄 Schedule regular database validation checks")
        recommendations.append("📊 Monitor data quality metrics over time")
        
        summary = {
            'total_records_checked': total_records,
            'total_issues_found': len(all_issues),
            'critical_issues': len(critical_issues),
            'warning_issues': len(warning_issues),
            'info_issues': len(info_issues),
            'table_statistics': table_info,
            'issues_by_type': {},
            'issues_by_table': {}
        }
        
        # Group issues by type and table
        for issue in all_issues:
            issue_type = issue.issue_type
            table = issue.table
            
            if issue_type not in summary['issues_by_type']:
                summary['issues_by_type'][issue_type] = 0
            summary['issues_by_type'][issue_type] += 1
            
            if table not in summary['issues_by_table']:
                summary['issues_by_table'][table] = 0
            summary['issues_by_table'][table] += 1
        
        report = ValidationReport(
            timestamp=datetime.now(),
            total_records_checked=total_records,
            issues_found=all_issues,
            summary=summary,
            recommendations=recommendations
        )
        
        logger.info(f"✅ Validation complete. Found {len(all_issues)} issues across {total_records} records")
        
        return report

# MCP-style tool functions
def validate_customer_records_tool(limit: int = 100) -> str:
    """MCP Tool: Validate customer records"""
    try:
        validator = DatabaseValidator()
        issues = validator.validate_customer_records()
        limited_issues = issues[:limit]
        
        result = {
            'tool': 'validate_customer_records',
            'timestamp': datetime.now().isoformat(),
            'total_issues_found': len(issues),
            'issues_returned': len(limited_issues),
            'issues': [asdict(issue) for issue in limited_issues]
        }
        
        return json.dumps(result, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

def find_duplicate_customers_tool(limit: int = 50) -> str:
    """MCP Tool: Find duplicate customers"""
    try:
        validator = DatabaseValidator()
        issues = validator.find_duplicate_customers()
        limited_issues = issues[:limit]
        
        result = {
            'tool': 'find_duplicate_customers',
            'timestamp': datetime.now().isoformat(),
            'total_duplicates_found': len(issues),
            'duplicates_returned': len(limited_issues),
            'duplicates': [asdict(issue) for issue in limited_issues]
        }
        
        return json.dumps(result, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

def generate_data_quality_report_tool() -> str:
    """MCP Tool: Generate comprehensive data quality report"""
    try:
        validator = DatabaseValidator()
        report = validator.generate_data_quality_report()
        
        # Convert report to dictionary for JSON serialization
        report_dict = asdict(report)
        
        # Add executive summary
        report_dict['executive_summary'] = {
            'database_health_score': max(0, 100 - (report.summary['critical_issues'] * 10 + report.summary['warning_issues'] * 2)),
            'critical_action_required': report.summary['critical_issues'] > 0,
            'data_quality_grade': 'A' if report.summary['critical_issues'] == 0 and report.summary['warning_issues'] < 5 
                                 else 'B' if report.summary['critical_issues'] == 0 and report.summary['warning_issues'] < 20
                                 else 'C' if report.summary['critical_issues'] < 5
                                 else 'D',
            'total_tables_analyzed': len(report.summary['table_statistics']),
            'most_problematic_table': max(report.summary['issues_by_table'].items(), key=lambda x: x[1])[0] if report.summary['issues_by_table'] else None
        }
        
        return json.dumps(report_dict, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

if __name__ == "__main__":
    print("🚀 HVAC Database Validation Agent")
    print("Available functions:")
    print("  - validate_customer_records_tool()")
    print("  - find_duplicate_customers_tool()")
    print("  - generate_data_quality_report_tool()")
    
    # Run a quick test
    print("\n🔍 Running quick validation test...")
    try:
        validator = DatabaseValidator()
        print("✅ Database connection successful")
        
        # Quick customer validation
        issues = validator.validate_customer_records()
        print(f"📊 Found {len(issues)} customer validation issues")
        
        # Quick duplicate check
        duplicates = validator.find_duplicate_customers()
        print(f"👥 Found {len(duplicates)} potential duplicate groups")
        
        print("\n✅ Validation agent is ready for use!")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")