#!/usr/bin/env python3
"""
HVAC CRM Database Validation MCP Agent - COMPLETE VERSION
Comprehensive Model Context Protocol agent for database consistency validation

🚀 PEŁNA MOC - FULL POWER IMPLEMENTATION
"""

import json
import logging
import re
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

import psycopg2
import psycopg2.extras

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_CONFIG = {
    'host': '**************',
    'port': '5432', 
    'database': 'hvacdb',
    'user': 'hvacdb',
    'password': 'blaeritipol'
}

class SeverityLevel(Enum):
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"

@dataclass
class ValidationIssue:
    table: str
    record_id: Optional[str]
    field: Optional[str]
    issue_type: str
    severity: SeverityLevel
    description: str
    suggested_fix: str
    sql_query: Optional[str] = None

@dataclass
class ValidationReport:
    timestamp: datetime
    total_records_checked: int
    issues_found: List[ValidationIssue]
    summary: Dict[str, Any]
    recommendations: List[str]

class HVACDatabaseValidator:
    """🔧 HVAC CRM Database Validation Engine - FULL POWER"""
    
    def __init__(self):
        self.conn = None
        self._connect()
        
    def _connect(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(**DATABASE_CONFIG)
            self.conn.autocommit = True
            logger.info("✅ Connected to HVAC CRM database")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
            
    def _execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute query and return results"""
        try:
            with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, params)
                return [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            return []
            
    def validate_customer_records(self) -> List[ValidationIssue]:
        """🔍 Validate customer records - COMPREHENSIVE CHECK"""
        issues = []
        
        # Check missing required fields
        required_checks = [
            ("name", "Customer name is required for identification"),
            ("email", "Email is required for communication"),
            ("phone", "Phone number is required for contact")
        ]
        
        for field, reason in required_checks:
            query = f"""
            SELECT id, {field}
            FROM customers 
            WHERE {field} IS NULL OR TRIM({field}) = ''
            LIMIT 100
            """
            missing_records = self._execute_query(query)
            
            for record in missing_records:
                issues.append(ValidationIssue(
                    table='customers',
                    record_id=str(record['id']),
                    field=field,
                    issue_type='missing_required_field',
                    severity=SeverityLevel.CRITICAL,
                    description=f"Customer {record['id']} missing {field}. {reason}",
                    suggested_fix=f"Update customer with valid {field}",
                    sql_query=f"UPDATE customers SET {field} = 'REQUIRED_VALUE' WHERE id = {record['id']}"
                ))
        
        # Validate email formats
        email_query = """
        SELECT id, email
        FROM customers 
        WHERE email IS NOT NULL 
        AND email NOT LIKE '%@%.%'
        LIMIT 100
        """
        invalid_emails = self._execute_query(email_query)
        
        for record in invalid_emails:
            issues.append(ValidationIssue(
                table='customers',
                record_id=str(record['id']),
                field='email',
                issue_type='invalid_email_format',
                severity=SeverityLevel.WARNING,
                description=f"Customer {record['id']} has invalid email: {record['email']}",
                suggested_fix="Update with valid email format (<EMAIL>)",
                sql_query=f"UPDATE customers SET email = '<EMAIL>' WHERE id = {record['id']}"
            ))
        
        # Check for very short phone numbers
        phone_query = """
        SELECT id, phone
        FROM customers 
        WHERE phone IS NOT NULL 
        AND LENGTH(REGEXP_REPLACE(phone, '[^0-9]', '', 'g')) < 7
        LIMIT 100
        """
        invalid_phones = self._execute_query(phone_query)
        
        for record in invalid_phones:
            issues.append(ValidationIssue(
                table='customers',
                record_id=str(record['id']),
                field='phone',
                issue_type='invalid_phone_format',
                severity=SeverityLevel.WARNING,
                description=f"Customer {record['id']} has too short phone: {record['phone']}",
                suggested_fix="Update with complete phone number",
                sql_query=f"UPDATE customers SET phone = 'CORRECTED_PHONE' WHERE id = {record['id']}"
            ))
        
        return issues
        
    def find_duplicate_customers(self) -> List[ValidationIssue]:
        """👥 Find duplicate customers - ADVANCED DETECTION"""
        issues = []
        
        # Find email duplicates (fixed query)
        email_duplicates_query = """
        SELECT 
            LOWER(email) as email_lower,
            array_agg(id) as customer_ids, 
            COUNT(*) as count
        FROM customers 
        WHERE email IS NOT NULL AND TRIM(email) != ''
        GROUP BY LOWER(email)
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT 50
        """
        email_duplicates = self._execute_query(email_duplicates_query)
        
        for duplicate in email_duplicates:
            customer_ids = duplicate['customer_ids']
            issues.append(ValidationIssue(
                table='customers',
                record_id=','.join(map(str, customer_ids)),
                field='email',
                issue_type='duplicate_email',
                severity=SeverityLevel.WARNING,
                description=f"Duplicate email '{duplicate['email_lower']}' in {duplicate['count']} records: {customer_ids}",
                suggested_fix="Merge duplicate records or update email addresses",
                sql_query=f"-- Review customers: {customer_ids}"
            ))
        
        # Find phone duplicates (fixed query)
        phone_duplicates_query = """
        SELECT 
            REGEXP_REPLACE(phone, '[^0-9]', '', 'g') as phone_clean,
            array_agg(id) as customer_ids, 
            COUNT(*) as count
        FROM customers 
        WHERE phone IS NOT NULL AND TRIM(phone) != ''
        GROUP BY REGEXP_REPLACE(phone, '[^0-9]', '', 'g')
        HAVING COUNT(*) > 1 AND LENGTH(REGEXP_REPLACE(phone, '[^0-9]', '', 'g')) > 6
        ORDER BY count DESC
        LIMIT 50
        """
        phone_duplicates = self._execute_query(phone_duplicates_query)
        
        for duplicate in phone_duplicates:
            customer_ids = duplicate['customer_ids']
            issues.append(ValidationIssue(
                table='customers',
                record_id=','.join(map(str, customer_ids)),
                field='phone',
                issue_type='duplicate_phone',
                severity=SeverityLevel.WARNING,
                description=f"Duplicate phone '{duplicate['phone_clean']}' in {duplicate['count']} records: {customer_ids}",
                suggested_fix="Merge duplicate records or update phone numbers",
                sql_query=f"-- Review customers: {customer_ids}"
            ))
        
        return issues
        
    def validate_foreign_keys(self) -> List[ValidationIssue]:
        """🔗 Validate foreign key relationships"""
        issues = []
        
        # Check orphaned addresses
        orphaned_addresses_query = """
        SELECT a.id, a.customer_id
        FROM addresses a
        LEFT JOIN customers c ON a.customer_id = c.id
        WHERE c.id IS NULL
        LIMIT 100
        """
        orphaned_addresses = self._execute_query(orphaned_addresses_query)
        
        for address in orphaned_addresses:
            issues.append(ValidationIssue(
                table='addresses',
                record_id=str(address['id']),
                field='customer_id',
                issue_type='orphaned_foreign_key',
                severity=SeverityLevel.CRITICAL,
                description=f"Address {address['id']} references non-existent customer {address['customer_id']}",
                suggested_fix="Delete orphaned address or create missing customer",
                sql_query=f"DELETE FROM addresses WHERE id = {address['id']}"
            ))
        
        # Check orphaned devices (if table exists)
        try:
            orphaned_devices_query = """
            SELECT d.id, d.customer_id
            FROM devices d
            LEFT JOIN customers c ON d.customer_id = c.id
            WHERE c.id IS NULL
            LIMIT 100
            """
            orphaned_devices = self._execute_query(orphaned_devices_query)
            
            for device in orphaned_devices:
                issues.append(ValidationIssue(
                    table='devices',
                    record_id=str(device['id']),
                    field='customer_id',
                    issue_type='orphaned_foreign_key',
                    severity=SeverityLevel.CRITICAL,
                    description=f"Device {device['id']} references non-existent customer {device['customer_id']}",
                    suggested_fix="Delete orphaned device or create missing customer",
                    sql_query=f"DELETE FROM devices WHERE id = {device['id']}"
                ))
        except:
            logger.info("Devices table not found or accessible")
        
        return issues
        
    def check_database_integrity(self) -> List[ValidationIssue]:
        """🛡️ Check database integrity"""
        issues = []
        
        # Check for NULL primary keys
        tables_to_check = ['customers', 'addresses']
        
        for table in tables_to_check:
            try:
                query = f"SELECT COUNT(*) as count FROM {table} WHERE id IS NULL"
                result = self._execute_query(query)
                
                if result and result[0]['count'] > 0:
                    issues.append(ValidationIssue(
                        table=table,
                        record_id=None,
                        field='id',
                        issue_type='null_primary_key',
                        severity=SeverityLevel.CRITICAL,
                        description=f"Found {result[0]['count']} NULL primary keys in {table}",
                        suggested_fix=f"Fix NULL primary keys in {table}",
                        sql_query=f"SELECT * FROM {table} WHERE id IS NULL"
                    ))
            except Exception as e:
                logger.warning(f"Could not check {table}: {e}")
        
        # Check email analysis data quality
        try:
            email_analysis_query = """
            SELECT id, sender, customer_info
            FROM enhanced_email_analysis
            WHERE customer_info IS NULL OR customer_info = '{}'
            LIMIT 50
            """
            empty_analysis = self._execute_query(email_analysis_query)
            
            for record in empty_analysis:
                issues.append(ValidationIssue(
                    table='enhanced_email_analysis',
                    record_id=str(record['id']),
                    field='customer_info',
                    issue_type='empty_ai_analysis',
                    severity=SeverityLevel.INFO,
                    description=f"Email analysis {record['id']} has empty customer_info",
                    suggested_fix="Reprocess email with AI analysis",
                    sql_query=f"-- Reprocess email analysis ID {record['id']}"
                ))
        except:
            logger.info("Enhanced email analysis table not accessible")
        
        return issues
        
    def analyze_data_completeness(self) -> List[ValidationIssue]:
        """📊 Analyze data completeness"""
        issues = []
        
        # Check customer profile completeness
        incomplete_profiles_query = """
        SELECT 
            id, name, email, phone, company, nip,
            CASE 
                WHEN name IS NULL OR TRIM(name) = '' THEN 0 ELSE 1 END +
                CASE 
                    WHEN email IS NULL OR TRIM(email) = '' THEN 0 ELSE 1 END +
                CASE 
                    WHEN phone IS NULL OR TRIM(phone) = '' THEN 0 ELSE 1 END +
                CASE 
                    WHEN company IS NULL OR TRIM(company) = '' THEN 0 ELSE 1 END +
                CASE 
                    WHEN nip IS NULL OR TRIM(nip) = '' THEN 0 ELSE 1 END
                as completeness_score
        FROM customers
        WHERE (
            CASE 
                WHEN name IS NULL OR TRIM(name) = '' THEN 0 ELSE 1 END +
            CASE 
                WHEN email IS NULL OR TRIM(email) = '' THEN 0 ELSE 1 END +
            CASE 
                WHEN phone IS NULL OR TRIM(phone) = '' THEN 0 ELSE 1 END +
            CASE 
                WHEN company IS NULL OR TRIM(company) = '' THEN 0 ELSE 1 END +
            CASE 
                WHEN nip IS NULL OR TRIM(nip) = '' THEN 0 ELSE 1 END
        ) < 3
        LIMIT 100
        """
        incomplete_profiles = self._execute_query(incomplete_profiles_query)
        
        for profile in incomplete_profiles:
            issues.append(ValidationIssue(
                table='customers',
                record_id=str(profile['id']),
                field='profile_completeness',
                issue_type='incomplete_profile',
                severity=SeverityLevel.INFO,
                description=f"Customer {profile['id']} has low completeness score: {profile['completeness_score']}/5",
                suggested_fix="Gather additional customer information",
                sql_query=f"SELECT * FROM customers WHERE id = {profile['id']}"
            ))
        
        return issues
        
    def generate_comprehensive_report(self) -> ValidationReport:
        """📋 Generate comprehensive validation report - FULL POWER"""
        logger.info("🚀 Starting COMPREHENSIVE database validation...")
        
        all_issues = []
        
        # Run all validation checks
        logger.info("🔍 Validating customer records...")
        all_issues.extend(self.validate_customer_records())
        
        logger.info("👥 Finding duplicate customers...")
        all_issues.extend(self.find_duplicate_customers())
        
        logger.info("🔗 Validating foreign keys...")
        all_issues.extend(self.validate_foreign_keys())
        
        logger.info("🛡️ Checking database integrity...")
        all_issues.extend(self.check_database_integrity())
        
        logger.info("📊 Analyzing data completeness...")
        all_issues.extend(self.analyze_data_completeness())
        
        # Get table statistics
        total_records = 0
        table_info = {}
        
        tables = ['customers', 'addresses', 'enhanced_email_analysis']
        for table in tables:
            try:
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                result = self._execute_query(count_query)
                count = result[0]['count'] if result else 0
                table_info[table] = {'row_count': count}
                total_records += count
            except Exception as e:
                logger.warning(f"Could not get count for {table}: {e}")
                table_info[table] = {'row_count': 0}
        
        # Categorize issues
        critical_issues = [i for i in all_issues if i.severity == SeverityLevel.CRITICAL]
        warning_issues = [i for i in all_issues if i.severity == SeverityLevel.WARNING]
        info_issues = [i for i in all_issues if i.severity == SeverityLevel.INFO]
        
        # Generate smart recommendations
        recommendations = []
        
        if critical_issues:
            recommendations.append("🚨 CRITICAL: Address critical issues immediately!")
            recommendations.append("🔧 Focus on missing required fields and orphaned records")
        
        if len([i for i in all_issues if i.issue_type == 'duplicate_email']) > 5:
            recommendations.append("📧 Implement email uniqueness validation")
        
        if len([i for i in all_issues if i.issue_type == 'duplicate_phone']) > 5:
            recommendations.append("📞 Implement phone number normalization")
        
        if warning_issues:
            recommendations.append("⚠️ Review data format issues for better quality")
        
        recommendations.extend([
            "🔄 Schedule automated daily validation checks",
            "📊 Monitor data quality KPIs over time",
            "🤖 Integrate validation with data entry processes",
            "📈 Set up alerts for critical data quality issues"
        ])
        
        # Build summary
        summary = {
            'total_records_checked': total_records,
            'total_issues_found': len(all_issues),
            'critical_issues': len(critical_issues),
            'warning_issues': len(warning_issues),
            'info_issues': len(info_issues),
            'table_statistics': table_info,
            'issues_by_type': {},
            'issues_by_table': {}
        }
        
        # Group issues
        for issue in all_issues:
            # By type
            if issue.issue_type not in summary['issues_by_type']:
                summary['issues_by_type'][issue.issue_type] = 0
            summary['issues_by_type'][issue.issue_type] += 1
            
            # By table
            if issue.table not in summary['issues_by_table']:
                summary['issues_by_table'][issue.table] = 0
            summary['issues_by_table'][issue.table] += 1
        
        report = ValidationReport(
            timestamp=datetime.now(),
            total_records_checked=total_records,
            issues_found=all_issues,
            summary=summary,
            recommendations=recommendations
        )
        
        logger.info(f"✅ COMPREHENSIVE validation complete! Found {len(all_issues)} issues across {total_records:,} records")
        
        return report

# MCP Tool Functions - FULL POWER IMPLEMENTATION
def validate_customer_records(limit: int = 100) -> str:
    """🔍 MCP Tool: Validate customer records"""
    try:
        validator = HVACDatabaseValidator()
        issues = validator.validate_customer_records()
        limited_issues = issues[:limit]
        
        result = {
            'tool': 'validate_customer_records',
            'timestamp': datetime.now().isoformat(),
            'total_issues_found': len(issues),
            'issues_returned': len(limited_issues),
            'issues': [asdict(issue) for issue in limited_issues]
        }
        
        return json.dumps(result, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

def find_duplicate_customers(limit: int = 50) -> str:
    """👥 MCP Tool: Find duplicate customers"""
    try:
        validator = HVACDatabaseValidator()
        issues = validator.find_duplicate_customers()
        limited_issues = issues[:limit]
        
        result = {
            'tool': 'find_duplicate_customers',
            'timestamp': datetime.now().isoformat(),
            'total_duplicates_found': len(issues),
            'duplicates_returned': len(limited_issues),
            'duplicates': [asdict(issue) for issue in limited_issues]
        }
        
        return json.dumps(result, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

def validate_foreign_keys() -> str:
    """🔗 MCP Tool: Validate foreign key relationships"""
    try:
        validator = HVACDatabaseValidator()
        issues = validator.validate_foreign_keys()
        
        result = {
            'tool': 'validate_foreign_keys',
            'timestamp': datetime.now().isoformat(),
            'total_foreign_key_issues': len(issues),
            'issues': [asdict(issue) for issue in issues]
        }
        
        return json.dumps(result, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

def check_database_integrity() -> str:
    """🛡️ MCP Tool: Check database integrity"""
    try:
        validator = HVACDatabaseValidator()
        issues = validator.check_database_integrity()
        
        result = {
            'tool': 'check_database_integrity',
            'timestamp': datetime.now().isoformat(),
            'total_integrity_issues': len(issues),
            'issues': [asdict(issue) for issue in issues]
        }
        
        return json.dumps(result, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

def generate_data_quality_report() -> str:
    """📋 MCP Tool: Generate comprehensive data quality report"""
    try:
        validator = HVACDatabaseValidator()
        report = validator.generate_comprehensive_report()
        
        # Convert to dict
        report_dict = asdict(report)
        
        # Add executive summary
        health_score = max(0, 100 - (report.summary['critical_issues'] * 10 + report.summary['warning_issues'] * 2))
        
        report_dict['executive_summary'] = {
            'database_health_score': health_score,
            'critical_action_required': report.summary['critical_issues'] > 0,
            'data_quality_grade': (
                'A' if health_score >= 90 else
                'B' if health_score >= 70 else
                'C' if health_score >= 50 else
                'D'
            ),
            'total_tables_analyzed': len(report.summary['table_statistics']),
            'most_problematic_table': (
                max(report.summary['issues_by_table'].items(), key=lambda x: x[1])[0] 
                if report.summary['issues_by_table'] else None
            ),
            'validation_timestamp': report.timestamp.isoformat(),
            'system_status': (
                'EXCELLENT' if health_score >= 90 else
                'GOOD' if health_score >= 70 else
                'NEEDS_ATTENTION' if health_score >= 50 else
                'CRITICAL'
            )
        }
        
        return json.dumps(report_dict, indent=2, default=str)
        
    except Exception as e:
        return json.dumps({'error': str(e)}, indent=2)

if __name__ == "__main__":
    print("🚀 HVAC CRM Database Validation MCP Agent - FULL POWER")
    print("=" * 70)
    print("Available MCP Tools:")
    print("  🔍 validate_customer_records() - Check customer data quality")
    print("  👥 find_duplicate_customers() - Detect duplicate entries")
    print("  🔗 validate_foreign_keys() - Check referential integrity")
    print("  🛡️ check_database_integrity() - Comprehensive integrity check")
    print("  📋 generate_data_quality_report() - Full quality assessment")
    print("=" * 70)
    
    # Run comprehensive test
    print("\n🔍 Running FULL POWER validation test...")
    try:
        validator = HVACDatabaseValidator()
        print("✅ Database connection successful")
        
        # Quick validation
        customer_issues = validator.validate_customer_records()
        duplicates = validator.find_duplicate_customers()
        fk_issues = validator.validate_foreign_keys()
        integrity_issues = validator.check_database_integrity()
        
        print(f"📊 Customer validation issues: {len(customer_issues)}")
        print(f"👥 Duplicate groups found: {len(duplicates)}")
        print(f"🔗 Foreign key issues: {len(fk_issues)}")
        print(f"🛡️ Integrity issues: {len(integrity_issues)}")
        
        print("\n✅ HVAC Database Validation MCP Agent is READY FOR FULL POWER OPERATION!")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")