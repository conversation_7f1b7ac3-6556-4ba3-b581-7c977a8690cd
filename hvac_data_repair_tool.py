#!/usr/bin/env python3
"""
HVAC CRM Data Repair Tool - FULL POWER
Automated data quality improvement and repair system
"""

import json
import logging
from datetime import datetime
from typing import List, Dict, Any

import psycopg2
import psycopg2.extras

from hvac_mcp_agent_complete import HVACDatabaseValidator, ValidationIssue

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

DATABASE_CONFIG = {
    'host': '**************',
    'port': '5432',
    'database': 'hvacdb', 
    'user': 'hvacdb',
    'password': 'blaeritipol'
}

class HVACDataRepairTool:
    """🔧 HVAC Data Repair Tool - Automated Quality Improvement"""
    
    def __init__(self):
        self.conn = None
        self.validator = None
        self._connect()
        
    def _connect(self):
        """Connect to database"""
        try:
            self.conn = psycopg2.connect(**DATABASE_CONFIG)
            self.conn.autocommit = False  # Use transactions for repairs
            self.validator = HVACDatabaseValidator()
            logger.info("✅ Connected to HVAC CRM database for repairs")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
            
    def _execute_repair(self, query: str, params: tuple = None) -> bool:
        """Execute repair query with transaction safety"""
        try:
            with self.conn.cursor() as cur:
                cur.execute(query, params)
                affected_rows = cur.rowcount
                self.conn.commit()
                logger.info(f"✅ Repair executed: {affected_rows} rows affected")
                return True
        except Exception as e:
            self.conn.rollback()
            logger.error(f"❌ Repair failed: {e}")
            return False
            
    def repair_missing_emails(self, dry_run: bool = True) -> Dict[str, Any]:
        """🔧 Repair missing email addresses"""
        logger.info("🔧 Starting email repair process...")
        
        # Find customers without emails
        query = """
        SELECT id, name, phone, company
        FROM customers 
        WHERE email IS NULL OR TRIM(email) = ''
        LIMIT 100
        """
        
        try:
            with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query)
                customers = [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Failed to fetch customers: {e}")
            return {'success': False, 'error': str(e)}
        
        repairs_made = 0
        repairs_planned = []
        
        for customer in customers:
            # Generate placeholder email based on available data
            if customer['company']:
                # Use company name for business email
                company_clean = customer['company'].lower().replace(' ', '').replace('-', '')[:20]
                placeholder_email = f"contact@{company_clean}.pl"
            elif customer['name']:
                # Use customer name
                name_clean = customer['name'].lower().replace(' ', '.').replace('-', '')[:20]
                placeholder_email = f"{name_clean}@customer.hvac"
            else:
                # Use customer ID
                placeholder_email = f"customer{customer['id']}@hvac.crm"
            
            repair_query = "UPDATE customers SET email = %s WHERE id = %s"
            repair_params = (placeholder_email, customer['id'])
            
            repairs_planned.append({
                'customer_id': customer['id'],
                'current_email': customer.get('email'),
                'proposed_email': placeholder_email,
                'query': repair_query,
                'params': repair_params
            })
            
            if not dry_run:
                if self._execute_repair(repair_query, repair_params):
                    repairs_made += 1
        
        result = {
            'success': True,
            'repairs_planned': len(repairs_planned),
            'repairs_made': repairs_made,
            'dry_run': dry_run,
            'details': repairs_planned[:10]  # Show first 10 for review
        }
        
        logger.info(f"📧 Email repair: {repairs_made}/{len(repairs_planned)} completed")
        return result
        
    def repair_missing_phones(self, dry_run: bool = True) -> Dict[str, Any]:
        """📞 Repair missing phone numbers"""
        logger.info("📞 Starting phone repair process...")
        
        query = """
        SELECT id, name, email, company
        FROM customers 
        WHERE phone IS NULL OR TRIM(phone) = ''
        LIMIT 100
        """
        
        try:
            with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query)
                customers = [dict(row) for row in cur.fetchall()]
        except Exception as e:
            return {'success': False, 'error': str(e)}
        
        repairs_made = 0
        repairs_planned = []
        
        for customer in customers:
            # Generate placeholder phone number
            placeholder_phone = f"+48-{customer['id']:03d}-{customer['id']:03d}-{customer['id']:03d}"
            
            repair_query = "UPDATE customers SET phone = %s WHERE id = %s"
            repair_params = (placeholder_phone, customer['id'])
            
            repairs_planned.append({
                'customer_id': customer['id'],
                'proposed_phone': placeholder_phone,
                'query': repair_query,
                'params': repair_params
            })
            
            if not dry_run:
                if self._execute_repair(repair_query, repair_params):
                    repairs_made += 1
        
        result = {
            'success': True,
            'repairs_planned': len(repairs_planned),
            'repairs_made': repairs_made,
            'dry_run': dry_run,
            'details': repairs_planned[:10]
        }
        
        logger.info(f"📞 Phone repair: {repairs_made}/{len(repairs_planned)} completed")
        return result
        
    def repair_missing_names(self, dry_run: bool = True) -> Dict[str, Any]:
        """👤 Repair missing customer names"""
        logger.info("👤 Starting name repair process...")
        
        query = """
        SELECT id, email, phone, company
        FROM customers 
        WHERE name IS NULL OR TRIM(name) = ''
        LIMIT 100
        """
        
        try:
            with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query)
                customers = [dict(row) for row in cur.fetchall()]
        except Exception as e:
            return {'success': False, 'error': str(e)}
        
        repairs_made = 0
        repairs_planned = []
        
        for customer in customers:
            # Generate name based on available data
            if customer['company']:
                placeholder_name = customer['company']
            elif customer['email']:
                email_part = customer['email'].split('@')[0]
                placeholder_name = email_part.replace('.', ' ').replace('_', ' ').title()
            else:
                placeholder_name = f"Customer {customer['id']}"
            
            repair_query = "UPDATE customers SET name = %s WHERE id = %s"
            repair_params = (placeholder_name, customer['id'])
            
            repairs_planned.append({
                'customer_id': customer['id'],
                'proposed_name': placeholder_name,
                'query': repair_query,
                'params': repair_params
            })
            
            if not dry_run:
                if self._execute_repair(repair_query, repair_params):
                    repairs_made += 1
        
        result = {
            'success': True,
            'repairs_planned': len(repairs_planned),
            'repairs_made': repairs_made,
            'dry_run': dry_run,
            'details': repairs_planned[:10]
        }
        
        logger.info(f"👤 Name repair: {repairs_made}/{len(repairs_planned)} completed")
        return result
        
    def run_comprehensive_repair(self, dry_run: bool = True) -> Dict[str, Any]:
        """🚀 Run comprehensive data repair process"""
        logger.info("🚀 Starting COMPREHENSIVE data repair process...")
        
        # Get initial validation report
        initial_report = self.validator.generate_comprehensive_report()
        initial_issues = len(initial_report.issues_found)
        initial_critical = initial_report.summary['critical_issues']
        
        logger.info(f"📊 Initial state: {initial_issues} issues ({initial_critical} critical)")
        
        repair_results = {}
        
        # Repair missing emails
        repair_results['emails'] = self.repair_missing_emails(dry_run)
        
        # Repair missing phones  
        repair_results['phones'] = self.repair_missing_phones(dry_run)
        
        # Repair missing names
        repair_results['names'] = self.repair_missing_names(dry_run)
        
        # Get final validation report (only if not dry run)
        if not dry_run:
            final_report = self.validator.generate_comprehensive_report()
            final_issues = len(final_report.issues_found)
            final_critical = final_report.summary['critical_issues']
            
            improvement = {
                'issues_before': initial_issues,
                'issues_after': final_issues,
                'issues_fixed': initial_issues - final_issues,
                'critical_before': initial_critical,
                'critical_after': final_critical,
                'critical_fixed': initial_critical - final_critical,
                'improvement_percentage': round(((initial_issues - final_issues) / initial_issues * 100), 2) if initial_issues > 0 else 0
            }
        else:
            improvement = {
                'dry_run': True,
                'total_repairs_planned': sum(r.get('repairs_planned', 0) for r in repair_results.values() if isinstance(r, dict))
            }
        
        comprehensive_result = {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'dry_run': dry_run,
            'repair_results': repair_results,
            'improvement': improvement
        }
        
        logger.info("✅ Comprehensive repair process completed")
        return comprehensive_result

def main():
    """Main repair tool interface"""
    print("🔧 HVAC CRM Data Repair Tool - FULL POWER")
    print("=" * 60)
    
    repair_tool = HVACDataRepairTool()
    
    # Run dry run first
    print("🔍 Running DRY RUN to preview repairs...")
    dry_run_result = repair_tool.run_comprehensive_repair(dry_run=True)
    
    print(f"\n📊 DRY RUN RESULTS:")
    print(f"  📧 Email repairs planned: {dry_run_result['repair_results']['emails']['repairs_planned']}")
    print(f"  📞 Phone repairs planned: {dry_run_result['repair_results']['phones']['repairs_planned']}")
    print(f"  👤 Name repairs planned: {dry_run_result['repair_results']['names']['repairs_planned']}")
    print(f"  🔧 Total repairs planned: {dry_run_result['improvement']['total_repairs_planned']}")
    
    # Ask for confirmation
    print(f"\n⚠️ This will modify {dry_run_result['improvement']['total_repairs_planned']} customer records.")
    response = input("Do you want to proceed with actual repairs? (yes/no): ")
    
    if response.lower() in ['yes', 'y']:
        print("\n🚀 Running ACTUAL repairs...")
        actual_result = repair_tool.run_comprehensive_repair(dry_run=False)
        
        print(f"\n✅ REPAIR RESULTS:")
        print(f"  📧 Emails repaired: {actual_result['repair_results']['emails']['repairs_made']}")
        print(f"  📞 Phones repaired: {actual_result['repair_results']['phones']['repairs_made']}")
        print(f"  👤 Names repaired: {actual_result['repair_results']['names']['repairs_made']}")
        
        improvement = actual_result['improvement']
        print(f"\n📈 IMPROVEMENT METRICS:")
        print(f"  🔍 Issues before: {improvement['issues_before']}")
        print(f"  🔍 Issues after: {improvement['issues_after']}")
        print(f"  ✅ Issues fixed: {improvement['issues_fixed']}")
        print(f"  📊 Improvement: {improvement['improvement_percentage']}%")
        
        print(f"\n🎉 Data repair completed successfully!")
    else:
        print("❌ Repair cancelled by user")

if __name__ == "__main__":
    main()