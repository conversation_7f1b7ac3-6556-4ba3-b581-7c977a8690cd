#!/usr/bin/env python3
"""
HVAC CRM Data Quality Dashboard - FULL POWER
Real-time monitoring and visualization of database quality metrics
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

from hvac_mcp_agent_complete import HVACDatabaseValidator

class HVACDataQualityDashboard:
    """📊 Real-time Data Quality Dashboard"""
    
    def __init__(self):
        self.validator = HVACDatabaseValidator()
        
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get current data quality metrics"""
        report = self.validator.generate_comprehensive_report()
        
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'health_score': max(0, 100 - (report.summary['critical_issues'] * 10 + report.summary['warning_issues'] * 2)),
            'total_records': report.total_records_checked,
            'total_issues': len(report.issues_found),
            'critical_issues': report.summary['critical_issues'],
            'warning_issues': report.summary['warning_issues'],
            'info_issues': report.summary['info_issues'],
            'table_stats': report.summary['table_statistics'],
            'issues_by_type': report.summary['issues_by_type'],
            'status': 'EXCELLENT' if report.summary['critical_issues'] == 0 and report.summary['warning_issues'] < 5 else
                     'GOOD' if report.summary['critical_issues'] == 0 and report.summary['warning_issues'] < 20 else
                     'NEEDS_ATTENTION' if report.summary['critical_issues'] < 5 else
                     'CRITICAL'
        }
        
        return metrics
        
    def display_dashboard(self):
        """Display real-time dashboard"""
        metrics = self.get_real_time_metrics()
        
        print("\n" + "="*80)
        print("📊 HVAC CRM DATA QUALITY DASHBOARD - LIVE MONITORING")
        print("="*80)
        print(f"⏰ Last Update: {metrics['timestamp']}")
        print(f"🏆 Health Score: {metrics['health_score']}/100")
        print(f"🎯 Status: {metrics['status']}")
        
        # Status indicator
        if metrics['status'] == 'EXCELLENT':
            print("🟢 Database quality is EXCELLENT")
        elif metrics['status'] == 'GOOD':
            print("🟡 Database quality is GOOD")
        elif metrics['status'] == 'NEEDS_ATTENTION':
            print("🟠 Database NEEDS ATTENTION")
        else:
            print("🔴 Database status is CRITICAL")
        
        print(f"\n📊 OVERVIEW:")
        print(f"  📋 Total Records: {metrics['total_records']:,}")
        print(f"  ⚠️ Total Issues: {metrics['total_issues']}")
        print(f"  🚨 Critical: {metrics['critical_issues']}")
        print(f"  ⚠️ Warnings: {metrics['warning_issues']}")
        print(f"  ℹ️ Info: {metrics['info_issues']}")
        
        print(f"\n🗂️ TABLE STATISTICS:")
        for table, stats in metrics['table_stats'].items():
            print(f"  📋 {table}: {stats['row_count']:,} records")
        
        if metrics['issues_by_type']:
            print(f"\n🔍 TOP ISSUES:")
            sorted_issues = sorted(metrics['issues_by_type'].items(), key=lambda x: x[1], reverse=True)
            for issue_type, count in sorted_issues[:5]:
                emoji = "🚨" if count > 50 else "⚠️" if count > 10 else "ℹ️"
                print(f"  {emoji} {issue_type}: {count}")
        
        print("="*80)

def main():
    """Main dashboard function"""
    dashboard = HVACDataQualityDashboard()
    
    print("🚀 Starting HVAC Data Quality Dashboard...")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            dashboard.display_dashboard()
            time.sleep(30)  # Update every 30 seconds
    except KeyboardInterrupt:
        print("\n👋 Dashboard monitoring stopped")

if __name__ == "__main__":
    main()